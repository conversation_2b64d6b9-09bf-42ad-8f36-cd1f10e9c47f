// ==UserScript==
// @name         浏览器搜索扩展工具(前端用)
// @version      1.2.0
// @namespace    http://tampermonkey.net/
// @description  划词搜索,一键跳转哔哩哔哩，谷歌，百度等。注：第一个图标为打开网址的按钮，仅当选中文本为链接时可用。
// <AUTHOR>
// @match        http://*/*
// @include      https://*/*
// @include      file:///*
// @run-at       document-end
// @grant        GM_setValue
// @grant        GM_getValue
// @license      GPL3.0
// ==/UserScript==

(function () {
  'use strict';

  // 配置常量
  const CONFIG = {
    ICON_SIZE: 22,
    POPUP_OFFSET: { x: 70, y: 40 },
    MIN_LEFT_MARGIN: 10,
    Z_INDEX: **********
  };

  // 样式定义
  const STYLES = {
    icon: `
      display: none !important;
      position: absolute !important;
      padding: 0 !important;
      margin: 0 !important;
      font-size: 13px !important;
      text-align: left !important;
      border: 0 !important;
      background: transparent !important;
      z-index: ${CONFIG.Z_INDEX} !important;
    `,
    button: `
      cursor: pointer !important;
      display: inline-block !important;
      width: ${CONFIG.ICON_SIZE}px !important;
      height: ${CONFIG.ICON_SIZE}px !important;
      border: 0 !important;
      background-color: rgba(255,255,255,1) !important;
      padding: 0 !important;
      margin: 0 5px 0 0 !important;
    `
  };

  // 搜索引擎配置
  const SEARCH_ENGINES = [
    {
      name: '打开',
      image: 'https://api.iconify.design/material-symbols-light:open-in-new.svg',
      host: [''],
      action: text => {
        const url = /^https?:\/\//.test(text) ? text : `http://${text}`;
        openUrl(url);
      }
    },
    {
      name: '哔哩哔哩',
      image: 'https://api.iconify.design/simple-icons:bilibili.svg?color=%2300A1D6',
      host: ['www.bilibili.com'],
      action: text => openUrl(`https://search.bilibili.com/video?keyword=${encodeURIComponent(text)}`)
    },
    {
      name: '谷歌',
      image: 'https://api.iconify.design/logos:google-icon.svg',
      host: ['www.google.com'],
      action: text => openUrl(`https://www.google.com/search?q=${encodeURIComponent(text)}`)
    },
    {
      name: 'Bing搜索',
      image: 'https://api.iconify.design/logos:bing.svg',
      host: ['www.baidu.com'],
      action: text => openUrl(`https://www.bing.com/search?q=${encodeURIComponent(text)}`)
    },
    {
      name: '火山翻译',
      image: 'https://api.iconify.design/icon-park-outline:translate.svg?color=%233445FF',
      host: ['translate.volcengine.com'],
      action: text => openUrl(`https://translate.volcengine.com/translate?text=${encodeURIComponent(text)}`)
    },
    {
      name: '掘金',
      image: 'https://api.iconify.design/simple-icons:juejin.svg?color=%23007FFF',
      host: ['juejin.im'],
      action: text => openUrl(`https://juejin.im/search?query=${encodeURIComponent(text)}&type=all`)
    },
    {
      name: 'GitHub',
      image: 'https://api.iconify.design/logos:github-icon.svg',
      host: ['github.com'],
      action: text => openUrl(`https://github.com/search?q=${encodeURIComponent(text)}`)
    },
    {
      name: 'MDN',
      image: 'https://api.iconify.design/simple-icons:mdnwebdocs.svg?color=%23000000',
      host: ['developer.mozilla.org/zh-CN/'],
      action: text => openUrl(`https://developer.mozilla.org/zh-CN/search?q=${encodeURIComponent(text)}`)
    }
  ];

  // 工具函数
  const utils = {
    getSelectedText: () => window.getSelection().toString().trim(),

    isTargetInToolbar: (target, toolbar) =>
      target === toolbar || (target.parentNode && target.parentNode === toolbar),

    calculatePosition: (e) => ({
      top: e.pageY + CONFIG.POPUP_OFFSET.y,
      left: Math.max(CONFIG.MIN_LEFT_MARGIN, e.pageX - CONFIG.POPUP_OFFSET.x)
    }),

    createElement: (tag, attributes = {}, styles = '') => {
      const element = document.createElement(tag);
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value);
      });
      if (styles) element.setAttribute('style', styles);
      return element;
    }
  };

  // 搜索管理器
  const searchManager = {
    saveSearch: (text) => GM_setValue('search', text),
    getSearch: () => GM_getValue('search'),
    clearSearch: () => GM_setValue('search', ''),

    executeSearch: (engine, text) => {
      this.saveSearch(text);
      engine.action(text);
    },

    handleCustomSearch: (customAction) => {
      const text = this.getSearch();
      this.clearSearch();
      if (customAction) customAction(text);
    }
  };

  // 工具栏管理器
  const toolbarManager = {
    toolbar: null,

    init() {
      this.createToolbar();
      this.bindEvents();
      this.handleCustomSearch();
    },

    createToolbar() {
      this.toolbar = utils.createElement('div', {}, STYLES.icon);

      SEARCH_ENGINES.forEach(engine => {
        const button = this.createButton(engine);
        this.toolbar.appendChild(button);
      });

      document.documentElement.appendChild(this.toolbar);
    },

    createButton(engine) {
      const button = utils.createElement('img', {
        src: engine.image,
        alt: engine.name,
        title: engine.name
      }, STYLES.button);

      button.addEventListener('mouseup', () => {
        const text = utils.getSelectedText();
        if (text) searchManager.executeSearch(engine, text);
      });

      return button;
    },

    show(position) {
      this.toolbar.style.top = `${position.top}px`;
      this.toolbar.style.left = `${position.left}px`;
      this.toolbar.style.display = 'block';
    },

    hide() {
      this.toolbar.style.display = 'none';
    },

    bindEvents() {
      // 防止选中文本消失
      document.addEventListener('mousedown', (e) => {
        if (utils.isTargetInToolbar(e.target, this.toolbar)) {
          e.preventDefault();
        }
      });

      // 监听选中变化
      document.addEventListener('selectionchange', () => {
        if (!utils.getSelectedText()) {
          this.hide();
        }
      });

      // 处理鼠标抬起事件
      document.addEventListener('mouseup', (e) => {
        if (utils.isTargetInToolbar(e.target, this.toolbar)) {
          e.preventDefault();
          return;
        }

        const text = utils.getSelectedText();
        if (text && this.toolbar.style.display === 'none') {
          this.show(utils.calculatePosition(e));
        } else if (!text) {
          this.hide();
        }
      });
    },

    handleCustomSearch() {
      const hostCustomMap = {};
      SEARCH_ENGINES.forEach(engine => {
        engine.host.forEach(host => {
          hostCustomMap[host] = engine.custom;
        });
      });

      const savedText = searchManager.getSearch();
      if (savedText && window.location.host in hostCustomMap) {
        searchManager.handleCustomSearch(hostCustomMap[window.location.host]);
      }
    }
  };

  // 在新标签页中打开URL
  function openUrl(url) {
    const win = window.open(url, '_blank');
    if (win && window.focus) {
      win.focus();
    }
    return win;
  }

  // 初始化
  toolbarManager.init();

})();