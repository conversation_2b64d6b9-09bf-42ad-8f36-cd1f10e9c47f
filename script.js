// ==UserScript==
// @name         浏览器搜索扩展工具(前端用)
// @version      1.1.0
// @namespace    http://tampermonkey.net/
// @description  划词搜索,一键跳转哔哩哔哩，谷歌，百度等。注：第一个图标为打开网址的按钮，仅当选中文本为链接时可用。
// <AUTHOR>
// @match        http://*/*
// @include      https://*/*
// @include      file:///*
// @run-at       document-end
// @grant        GM_setValue
// @grant        GM_getValue
// @license      GPL3.0
// ==/UserScript==

(function () {
  'use strict';

  // 简化关键词处理对象
  const keyword = {
    beforePopup(popup) {
      const text = window.getSelection().toString().trim();
      GM_setValue('search', text);
      popup(text);
    },
    beforeCustom(custom) {
      const text = GM_getValue('search');
      GM_setValue('search', '');
      custom(text);
    }
  };

  // 搜索引擎配置数组
  const iconArray = [
    {
      name: '打开',
      image: 'https://api.iconify.design/material-symbols-light:open-in-new.svg',
      host: [''],
      popup(text) {
        const url = text.startsWith('http://') || text.startsWith('https://')
          ? text
          : `http://${text}`;
        window.open(url, '_blank');
      }
    },
    {
      name: '哔哩哔哩',
      image: 'https://api.iconify.design/simple-icons:bilibili.svg',
      host: ['www.bilibili.com'],
      popup(text) {
        open(`https://search.bilibili.com/video?keyword=${encodeURIComponent(text)}`);
      }
    },
    {
      name: '谷歌',
      image: 'https://api.iconify.design/simple-icons:google.svg',
      host: ['www.google.com'],
      popup: function (text) {
        open('https://www.google.com/search?q=' + encodeURIComponent(text));
      }
    },
    {
      name: 'bing搜索',
      image: 'https://api.iconify.design/simple-icons:microsoftbing.svg',
      host: ['www.baidu.com'],
      popup: function (text) {
        open('https://www.bing.com/search?q=' + encodeURIComponent(text));
      }
    },
    {
      name: '火山翻译',
      image: 'https://api.iconify.design/icon-park-outline:translate.svg',
      host: ['translate.volcengine.com'],
      popup: function (text) {
        open('https://translate.volcengine.com/translate?text=' + encodeURIComponent(text));
      }
    },
    {
      name: '掘金',
      image: 'https://api.iconify.design/simple-icons:juejin.svg',
      host: ['juejin.im'],
      popup: function (text) {
        open('https://juejin.im/search?query=' + encodeURIComponent(text) + '&type=all');
      }
    },
    {
      name: 'github',
      image: 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxOC4xLjEsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHZpZXdCb3g9IjAgMCAzNiAzNiIgZW5hYmxlLWJhY2tncm91bmQ9Im5ldyAwIDAgMzYgMzYiIHhtbDpzcGFjZT0icHJlc2VydmUiPg0KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGZpbGw9IiMxOTE3MTciIGQ9Ik0xOCwxLjRDOSwxLjQsMS43LDguNywxLjcsMTcuN2MwLDcuMiw0LjcsMTMuMywxMS4xLDE1LjUNCgljMC44LDAuMSwxLjEtMC40LDEuMS0wLjhjMC0wLjQsMC0xLjQsMC0yLjhjLTQuNSwxLTUuNS0yLjItNS41LTIuMmMtMC43LTEuOS0xLjgtMi40LTEuOC0yLjRjLTEuNS0xLDAuMS0xLDAuMS0xDQoJYzEuNiwwLjEsMi41LDEuNywyLjUsMS43YzEuNSwyLjUsMy44LDEuOCw0LjcsMS40YzAuMS0xLjEsMC42LTEuOCwxLTIuMmMtMy42LTAuNC03LjQtMS44LTcuNC04LjFjMC0xLjgsMC42LTMuMiwxLjctNC40DQoJYy0wLjItMC40LTAuNy0yLjEsMC4yLTQuM2MwLDAsMS40LTAuNCw0LjUsMS43YzEuMy0wLjQsMi43LTAuNSw0LjEtMC41YzEuNCwwLDIuOCwwLjIsNC4xLDAuNWMzLjEtMi4xLDQuNS0xLjcsNC41LTEuNw0KCWMwLjksMi4yLDAuMywzLjksMC4yLDQuM2MxLDEuMSwxLjcsMi42LDEuNyw0LjRjMCw2LjMtMy44LDcuNi03LjQsOGMwLjYsMC41LDEuMSwxLjUsMS4xLDNjMCwyLjIsMCwzLjksMCw0LjUNCgljMCwwLjQsMC4zLDAuOSwxLjEsMC44YzYuNS0yLjIsMTEuMS04LjMsMTEuMS0xNS41QzM0LjMsOC43LDI3LDEuNCwxOCwxLjR6Ii8+DQo8L3N2Zz4NCg==',
      host: ['github.com'],
      popup: function (text) {
        open('https://github.com/search?q=' + encodeURIComponent(text));
      }
    },
    {
      name: 'MDN',
      image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAABX1BMVEX///9HR0tTU1chICZGRUpDQkdEQ0gVFBrOzs/My80eHSNRUFT+/v4YFx0WFRvw8PE7Oj/29vb8/PwuLTLl5eZLSk8aGR6Uk5be3t8kIyh0c3b5+fkzMjhzcnaEhIeIh4pWVVrNzM68vL3Av8H5+fo/PkNnZmqvr7HQ0NF3d3p9fH83NjtjYmYjIierq601NDmfnqGOjpHr6+vX1tcnJiykpKZcW1+Qj5LFxcbW1tdsbG+1tLbg4ODY2NmgoKI8PEGYmJpYV1xSUlYxMDVQT1MbGiA/P0Sbm53S0tM8O0CsrK5xcHRpaGx5eHzj4+SxsbPs7OxgYGTCwcM2NTpOTVKpqKu5ubvy8vLt7e1/f4L6+vtIR0ynp6mDg4ZCQUaurq9MS1D09PTIyMleXmKKio23t7lHRkqrqqzT09T9/f2ysrTNzc5iYWVvbnJpaW0sKzC7u70pKC56eX3CwsQmJitiaC63AAABV0lEQVRIx+3VRVMDQRCG4Xc3sAOEEEhwEtzd3d3d3d3h/xeTymbn1lVcIXOaPjzVffh6BvXLQxL8DeCzbfvc7/e5ZbZtb/v9jgCW4OIJUtxyHs4gTQC78JjugalMKBZBRg68GrACwVsRbEDo24BNuEsVwQ58KQ8UHcKzCAL78GnAMpT4RDANRAxYg3clgnG4Vx6Y2YOoDIbh2oAJeHgTQY2e6NSAUUhXIiiD1SwPZOXCiQzqYU55wNL9skUQ1kGYNKAPCpUIGnQQ8g0YgQIZVEGr8sCWnihPBC3V0GzAAXQGRFAbC4IBl9CjRFAHlcoDHyHoFUF+UC+LAS+QkyGCcsgMG3ADQ0oE7VChPBBbvUERxIJQasCVXr1uEaTEg5AAx9CvRNARD4ILjtahUQZd8SC4IKr7NYmgzQ2CCxZhQImgyHGcgHtfsKxZx4kknirLGkt+KP8Q/ABoHVFTkXMUNgAAAABJRU5ErkJggg==',
      host: ['developer.mozilla.org/zh-CN/'],
      popup: function (text) {
        open('https://developer.mozilla.org/zh-CN/search?q=' + encodeURIComponent(text));
      }
    },
  ],
    hostCustomMap = {};
  iconArray.forEach(function (obj) {
    obj.host.forEach(function (host) {// 赋值DOM加载后的自定义方法Map
      hostCustomMap[host] = obj.custom;
    });
  });
  var text = GM_getValue('search');
  if (text && window.location.host in hostCustomMap) {
    keyword.beforeCustom(hostCustomMap[window.location.host]);
  }
  var icon = document.createElement('div');
  iconArray.forEach(function (obj) {
    var img = document.createElement('img');
    img.setAttribute('src', obj.image);
    img.setAttribute('alt', obj.name);
    img.setAttribute('title', obj.name);
    img.addEventListener('mouseup', function () {
      keyword.beforePopup(obj.popup);
    });
    img.setAttribute('style', '' +
      'cursor:pointer!important;' +
      'display:inline-block!important;' +
      'width:22px!important;' +//图标尺寸设置
      'height:22px!important;' +//图标尺寸设置
      'border:0!important;' +
      'background-color:rgba(255,255,255,1)!important;' +
      'padding:0!important;' +
      'margin:0!important;' +
      'margin-right:5px!important;' +
      '');
    icon.appendChild(img);
  });
  icon.setAttribute('style', '' +
    'display:none!important;' +
    'position:absolute!important;' +
    'padding:0!important;' +
    'margin:0!important;' +
    'font-size:13px!important;' +
    'text-align:left!important;' +
    'border:0!important;' +
    'background:transparent!important;' +
    'z-index:2147483647!important;' +
    '');
  // 添加到 DOM
  document.documentElement.appendChild(icon);
  // 鼠标事件：防止选中的文本消失
  document.addEventListener('mousedown', function (e) {
    if (e.target == icon || (e.target.parentNode && e.target.parentNode == icon)) {
      e.preventDefault();
    }
  });
  // 选中变化事件：
  document.addEventListener("selectionchange", function () {
    if (!window.getSelection().toString().trim()) {
      icon.style.display = 'none';
    }
  });
  // 鼠标事件
  document.addEventListener('mouseup', function (e) {
    if (e.target == icon || (e.target.parentNode && e.target.parentNode == icon)) {
      e.preventDefault();
      return;
    }
    var text = window.getSelection().toString().trim();
    if (text && icon.style.display == 'none') {
      icon.style.top = e.pageY + 40 + 'px';
      if (e.pageX - 70 < 10)
        icon.style.left = '10px';
      else
        icon.style.left = e.pageX - 70 + 'px';
      icon.style.display = 'block';
    } else if (!text) {
      icon.style.display = 'none';
    }
  });



  /**触发事件*/
  function tiggerEvent(el, type) {
    if ('createEvent' in document) {// modern browsers, IE9+
      var e = document.createEvent('HTMLEvents');
      e.initEvent(type, false, true);// event.initEvent(type, bubbles, cancelable);
      el.dispatchEvent(e);
    } else {// IE 8
      e = document.createEventObject();
      e.eventType = type;
      el.fireEvent('on' + e.eventType, e);
    }
  }

  /**在新标签页中打开*/
  function open(url) {
    var win;
    win = window.open(url);
    if (window.focus) {
      win.focus();
    }
    return win;
  }

})();